
/**
 * High-Performance Transparent Proxy Server
 * Features:
 * - No KV storage for maximum speed
 * - Domain whitelist control via global variable
 * - Environment variable based URL routing
 * - Pure transparent data forwarding
 */

// Global configuration - Domain whitelist
const ALLOWED_DOMAINS: string[] = Deno.env.get("ALLOWED_DOMAINS")?.split(",") || ["*"];

// Environment variable for URL prefix routing
const PROXY_PREFIX = Deno.env.get("PROXY_PREFIX") || "/proxy/";

// CORS configuration from environment variables
const CORS_ENABLED = Deno.env.get("CORS_ENABLED")?.toLowerCase() === "true" || true; // Default enabled
const CORS_ORIGIN = Deno.env.get("CORS_ORIGIN") || "*";
const CORS_METHODS = Deno.env.get("CORS_METHODS") || "GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH";
const CORS_HEADERS = Deno.env.get("CORS_HEADERS") || "*";
const CORS_MAX_AGE = Deno.env.get("CORS_MAX_AGE") || "86400";
const CORS_CREDENTIALS = Deno.env.get("CORS_CREDENTIALS")?.toLowerCase() === "true" || false;

// Performance optimization: Pre-compile regex patterns
const PROXY_PREFIX_REGEX = new RegExp(`^${PROXY_PREFIX.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`);

/**
 * Check if domain is allowed based on whitelist
 * @param domain - Domain to check
 * @returns boolean - Whether domain is allowed
 */
function isDomainAllowed(domain: string): boolean {
  // If wildcard is present, allow all domains
  if (ALLOWED_DOMAINS.includes("*")) {
    return true;
  }

  // Check exact domain match or subdomain match
  return ALLOWED_DOMAINS.some(allowedDomain => {
    if (allowedDomain.startsWith("*.")) {
      // Subdomain wildcard matching
      const baseDomain = allowedDomain.slice(2);
      return domain === baseDomain || domain.endsWith(`.${baseDomain}`);
    }
    return domain === allowedDomain;
  });
}

/**
 * Extract target URL from request path
 * @param pathname - Request pathname
 * @returns string | null - Target URL or null if invalid
 */
function extractTargetUrl(pathname: string): string | null {
  if (!PROXY_PREFIX_REGEX.test(pathname)) {
    return null;
  }

  // Remove proxy prefix and get the target URL
  const targetUrl = pathname.slice(PROXY_PREFIX.length);

  // Validate URL format
  try {
    const url = new URL(targetUrl);

    // Check domain whitelist
    if (!isDomainAllowed(url.hostname)) {
      return null;
    }

    return targetUrl;
  } catch {
    return null;
  }
}

/**
 * Create optimized headers for proxy response
 * @param originalHeaders - Original response headers
 * @returns Headers - Optimized headers
 */
function createProxyHeaders(originalHeaders: Headers): Headers {
  const headers = new Headers(originalHeaders);

  // Remove hop-by-hop headers for transparent proxying
  const hopByHopHeaders = [
    'connection',
    'keep-alive',
    'proxy-authenticate',
    'proxy-authorization',
    'te',
    'trailers',
    'transfer-encoding',
    'upgrade'
  ];

  hopByHopHeaders.forEach(header => headers.delete(header));

  // Add CORS headers if enabled
  if (CORS_ENABLED) {
    headers.set('Access-Control-Allow-Origin', CORS_ORIGIN);
    headers.set('Access-Control-Allow-Methods', CORS_METHODS);
    headers.set('Access-Control-Allow-Headers', CORS_HEADERS);
    headers.set('Access-Control-Max-Age', CORS_MAX_AGE);
    
    if (CORS_CREDENTIALS) {
      headers.set('Access-Control-Allow-Credentials', 'true');
    }
  }

  return headers;
}

/**
 * Handle CORS preflight OPTIONS request
 * @param req - Request object
 * @returns Response - CORS preflight response
 */
function handleCorsPreflightRequest(req: Request): Response {
  const headers = new Headers();
  
  if (CORS_ENABLED) {
    headers.set('Access-Control-Allow-Origin', CORS_ORIGIN);
    headers.set('Access-Control-Allow-Methods', CORS_METHODS);
    headers.set('Access-Control-Allow-Headers', CORS_HEADERS);
    headers.set('Access-Control-Max-Age', CORS_MAX_AGE);
    
    if (CORS_CREDENTIALS) {
      headers.set('Access-Control-Allow-Credentials', 'true');
    }
  }

  return new Response(null, {
    status: 200,
    headers: headers
  });
}

// Main server handler
Deno.serve(async (req: Request): Promise<Response> => {
  const url = new URL(req.url);

  // Handle CORS preflight requests
  if (req.method === "OPTIONS" && CORS_ENABLED) {
    const targetUrl = extractTargetUrl(url.pathname);
    if (targetUrl) {
      return handleCorsPreflightRequest(req);
    }
  }

  // Extract target URL from path
  const targetUrl = extractTargetUrl(url.pathname);

  if (!targetUrl) {
    // Return usage information for non-proxy requests
    const responseHeaders = new Headers({ "Content-Type": "text/plain; charset=utf-8" });
    
    if (CORS_ENABLED) {
      responseHeaders.set("Access-Control-Allow-Origin", CORS_ORIGIN);
      responseHeaders.set("Access-Control-Allow-Methods", CORS_METHODS);
      responseHeaders.set("Access-Control-Allow-Headers", CORS_HEADERS);
    }

    return new Response(
      `Transparent Proxy Server\n\n` +
      `Usage: ${PROXY_PREFIX}<target-url>\n` +
      `Example: ${PROXY_PREFIX}https://example.com/api/data\n\n` +
      `Allowed domains: ${ALLOWED_DOMAINS.join(", ")}\n` +
      `Environment variables:\n` +
      `- PROXY_PREFIX: ${PROXY_PREFIX}\n` +
      `- ALLOWED_DOMAINS: ${ALLOWED_DOMAINS.join(",")}\n` +
      `- CORS_ENABLED: ${CORS_ENABLED}\n` +
      `- CORS_ORIGIN: ${CORS_ORIGIN}`,
      {
        status: 200,
        headers: responseHeaders
      }
    );
  }

  // Construct final URL with query parameters
  const finalUrl = targetUrl + url.search;

  try {
    // Create transparent proxy request
    const proxyRequest = new Request(finalUrl, {
      method: req.method,
      headers: req.headers,
      body: req.body,
      // Enable connection reuse for better performance
      keepalive: true,
    });

    // Forward request to target
    const targetResponse = await fetch(proxyRequest);

    // Create optimized response headers
    const responseHeaders = createProxyHeaders(targetResponse.headers);

    // Return transparent response with streaming body
    return new Response(targetResponse.body, {
      status: targetResponse.status,
      statusText: targetResponse.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    // Return error response
    return new Response(
      `Proxy Error: ${error instanceof Error ? error.message : String(error)}`,
      {
        status: 502,
        headers: { "Content-Type": "text/plain; charset=utf-8" }
      }
    );
  }
});